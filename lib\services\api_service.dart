import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../presentation/user_homepage_screen/job_seeker_onboarding.dart';
import 'api_client.dart';
import 'user_api_endpoints.dart';
import 'session_manager.dart';

/// Service layer for API calls using the ApiClient
class ApiService {
  static final ApiClient _apiClient = ApiClient.instance;

  /// Login user with email and password
  static Future<Response> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiClient.post(
        UserApiEndpoints.login,
        data: {
          'email': email,
          'password': password,
        },
      );

      // Extract access token from the response and set auth token
      if (response.statusCode == 200 && response.data['access_token'] != null) {
        final data = response.data;

        // Set access token
        _apiClient.setAuthToken(data['access_token']);

        // Calculate token expiry from expires_in timestamp
        DateTime? tokenExpiry;
        if (data['expires_in'] != null) {
          tokenExpiry =
              DateTime.fromMillisecondsSinceEpoch(data['expires_in'] * 1000);
        } else {
          // Fallback to 24 hours if expires_in not provided
          tokenExpiry = DateTime.now().add(const Duration(hours: 24));
        }

        // Save session using SessionManager
        await SessionManager.instance.saveSession(
          accessToken: data['access_token'],
          refreshToken: data['refresh_token'],
          userId: data['user_id'],
          userType: data['user_type'],
          user: data['user'],
          tokenExpiry: tokenExpiry,
        );

        // Also save to SharedPreferences for backward compatibility
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('access_token', data['access_token']);
        await prefs.setString('refresh_token', data['refresh_token']);
        await prefs.setString('user_id', data['user_id']);
        await prefs.setString('user_type', data['user_type']);

        // Save user object as JSON string
        final userJson = jsonEncode(data['user']);
        await prefs.setString('user', userJson);
      }

      return response;
    } catch (e) {
      rethrow;
    }
  }

  /// Register new user
  // static Future<Response> register({
  //   required String name,
  //   required String email,
  //   required String password,
  //   required String userType,
  // }) async {
  //   try {
  //     return await _apiClient.post(
  //       UserApiEndpoints.register,
  //       data: {
  //         'name': name,
  //         'email': email,
  //         'password': password,
  //         'user_type': userType,
  //       },
  //     );
  //   } catch (e) {
  //     rethrow;
  //   }
  // }
  static Future<Response> register({
    required String name,
    required String email,
    required String password,
    required String userType,
  }) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.register,
        data: {
          'name': name,
          'email': email,
          'password': password,
          'user_type': userType,
        },
      );
    } on DioException catch (e) {
      // Return the error response instead of rethrowing
      if (e.response != null) {
        return e.response!;
      }
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  /// Google Sign in
  static Future<Response> googleSignIn(
      {required String idToken, String? userType}) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.googleSignIn,
        data: {
          'id_token': idToken,
          'user_type': userType,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Logout user
  static Future<Response> logout() async {
    try {
      final response = await _apiClient.post(UserApiEndpoints.userLogout);

      // Clear stored token and session
      _apiClient.clearAuthToken();
      await SessionManager.instance.clearSession();

      // Also clear SharedPreferences for backward compatibility
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('access_token');
      await prefs.remove('refresh_token');
      await prefs.remove('user_id');
      await prefs.remove('user_type');
      await prefs.remove('user');

      return response;
    } catch (e) {
      rethrow;
    }
  }

  /// Forgot password
  static Future<Response> forgotPassword({required String email}) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.forgotPassword,
        data: {'email': email},
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Reset password without token
  static Future<Response> resetAPassword({
    required String email,
    required String oldPassword,
    required String password,
  }) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.resetPassword,
        data: {
          'email': email,
          'old_password': oldPassword,
          'new_password': password,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Verify OTP
  static Future<Response> verifyOtp({
    required String email,
    required String otp,
  }) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.verifyUser,
        data: {
          'email': email,
          'otp': otp,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Resend OTP
  static Future<Response> resendOtp({required String email}) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.resendOtp,
        data: {'email': email},
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Get all jobs with optional filters
  static Future<Response> getJobs({
    int page = 1,
    int limit = 10,
    String? status,
    String? search,
  }) async {
    try {
      Map<String, dynamic> queryParams = {
        'page': page,
        'limit': limit,
      };

      if (status != null) queryParams['status'] = status;
      if (search != null) queryParams['search'] = search;

      return await _apiClient.get(
        UserApiEndpoints.allJobPosts,
        queryParameters: queryParams,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Apply for a job
  static Future<Response> applyForJob({
    Map<String, dynamic>? applicationData,
  }) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.applyForJob,
        data: applicationData,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Get notifications
  static Future<Response> getNotifications({
    int page = 1,
    int limit = 10,
    String? search,
    String? type,
    String? read,
    String? userId,
  }) async {
    try {
      FormData formData = FormData.fromMap({
        'user_id': userId,
      });
      return await _apiClient.post(
        UserApiEndpoints.getUserNotificationsUrl(),
        data: formData,
        queryParameters: {
          'page': page,
          'limit': limit,
          'search': search,
          'type': type,
          'read': read,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Mark notification as read
  static Future<Response> markNotificationAsRead(
      String notificationId, String userId) async {
    try {
      return await _apiClient.patch(
        '${UserApiEndpoints.markNotificationAsRead}/$notificationId/$userId',
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Get user favorites
  static Future<Response> getUserFavoriteNews({
    String? user_id,
    int page = 1,
    int limit = 10,
    String? search,
    String? status,
    String? sort,
  }) async {
    try {
      return await _apiClient.get(
          '${UserApiEndpoints.getUserFavoriteNews}/$user_id',
          queryParameters: {
            'page': page,
            'limit': limit,
            'search': search,
            'status': status,
            'sort': sort,
          });
    } catch (e) {
      rethrow;
    }
  }

  /// Add news to user favorites
  static Future<Response> addNewsToUserFavorites(
      String newsId, String userId) async {
    try {
      // Create FormData object
      FormData formData = FormData.fromMap({
        'news_id': newsId,
        'user_id': userId,
      });

      return await _apiClient.post(
        UserApiEndpoints.addNewsToFavorites,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Remove news from user favorites
  static Future<Response> removeNewsFromUserFavorites(
      String newsId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'news_id': newsId,
        'user_id': userId,
      });
      return await _apiClient.delete(
        UserApiEndpoints.removeNewsFromFavorites,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Get user fav events
  static Future<Response> getUserFavoriteEvents({
    String? user_id,
    int page = 1,
    int limit = 10,
    String? search,
    String? status,
    String? sort,
  }) async {
    try {
      return await _apiClient.get(
          '${UserApiEndpoints.getUserFavoriteEvents}/$user_id',
          queryParameters: {
            'page': page,
            'limit': limit,
            'search': search,
            'status': status,
            'sort': sort,
          });
    } catch (e) {
      rethrow;
    }
  }

  // Add event to user favorites
  static Future<Response> addEventToUserFavorites(
      String eventId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
      });
      return await _apiClient.post(
        UserApiEndpoints.addEventToFavorites,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Remove event from user favorites
  static Future<Response> removeEventFromUserFavorites(
      String eventId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
      });
      return await _apiClient.delete(
        UserApiEndpoints.removeEventFromFavorites,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Check if user liked event
  static Future<Response> checkIfUserLikedEvent(
      String eventId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
      });
      return await _apiClient.post(
        UserApiEndpoints.checkIfUserLikedEvent,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // user to like event
  static Future<Response> userToLikeEvent(String eventId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
      });
      return _apiClient.post(
        UserApiEndpoints.userToLikeEvent,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // user to unlike event
  static Future<Response> userToUnLikeEvent(
      String eventId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
      });
      return _apiClient.post(
        UserApiEndpoints.userToUnlikeEvent,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // user to send event opinion
  static Future<Response> userToSendEventOpinion(
      String eventId, String userId, String content) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
        'content': content,
      });
      return _apiClient.post(
        UserApiEndpoints.sendEventOpinion,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // user to share event
  static Future<Response> userToShareEvent(
      String eventId, String userId, String platform) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
        'platform': platform,
      });
      return _apiClient.post(
        UserApiEndpoints.userToShareEvent,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Get user fav job posts
  static Future<Response> getUserFavoriteJobPost({
    String? user_id,
    int page = 1,
    int limit = 10,
    String? search,
    String? status,
    String? sort,
  }) async {
    try {
      return await _apiClient.get(
          '${UserApiEndpoints.getUserFavoriteJobPosts}/$user_id',
          queryParameters: {
            'page': page,
            'limit': limit,
            'search': search,
            'status': status,
            'sort': sort,
          });
    } catch (e) {
      rethrow;
    }
  }

  // Add job post to user favorites
  static Future<Response> addJobPostToUserFavorites(
      String jobId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'job_post_id': jobId,
        'user_id': userId,
      });
      return await _apiClient.post(
        UserApiEndpoints.addFavoriteJobPost,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Remove job post from user favorites
  static Future<Response> removeJobPostFromUserFavorites(
      String jobId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'job_post_id': jobId,
        'user_id': userId,
      });
      return await _apiClient.delete(
        UserApiEndpoints.removeFavoriteJobPost,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Get Popular Events
  static Future<Response> getPopularEvents() async {
    try {
      return await _apiClient.get(UserApiEndpoints.popularEvents);
    } catch (e) {
      rethrow;
    }
  }

  // Get Events
  static Future<Response> getEvents({
    int page = 1,
    int limit = 10,
    String? search,
    String? category_id,
  }) async {
    try {
      return await _apiClient.get(UserApiEndpoints.allEvents, queryParameters: {
        'page': page,
        'limit': limit,
        'search': search,
        'category_id': category_id,
      });
    } catch (e) {
      rethrow;
    }
  }

  // Get Event by Id
  static Future<Response> getEventById(String eventId) async {
    try {
      return await _apiClient.get('${UserApiEndpoints.eventById}/$eventId');
    } catch (e) {
      rethrow;
    }
  }

  // Get Event Category by Id
  static Future<Response> getEventCategoryById(String categoryId) async {
    try {
      return await _apiClient
          .get('${UserApiEndpoints.eventCategoryById}/$categoryId');
    } catch (e) {
      rethrow;
    }
  }

  // Get All Event Categories
  static Future<Response> getEventCategories() async {
    try {
      return await _apiClient.get(UserApiEndpoints.eventCategory);
    } catch (e) {
      rethrow;
    }
  }

  //Get Blogs
  static Future<Response> getBlogs({
    int page = 1,
    int limit = 10,
    String? search,
    String? category_id,
  }) async {
    try {
      return await _apiClient.get(UserApiEndpoints.allBlogs, queryParameters: {
        'page': page,
        'limit': limit,
        'search': search,
        'category_id': category_id,
      });
    } catch (e) {
      rethrow;
    }
  }

  // Get Blog by Id
  static Future<Response> getBlogById(String blogId) async {
    try {
      return await _apiClient.get('${UserApiEndpoints.blogById}/$blogId');
    } catch (e) {
      rethrow;
    }
  }

  // Get Blog Category by Id
  static Future<Response> getBlogCategoryById(String categoryId) async {
    try {
      return await _apiClient
          .get('${UserApiEndpoints.blogCategoryById}/$categoryId');
    } catch (e) {
      rethrow;
    }
  }

  // Get All Blog Categories
  static Future<Response> getBlogCategories() async {
    try {
      return await _apiClient.get(UserApiEndpoints.blogCategory);
    } catch (e) {
      rethrow;
    }
  }

  //Get News
  static Future<Response> getNews({
    int page = 1,
    int limit = 10,
    String? search,
    String? category_id,
  }) async {
    try {
      return await _apiClient.get(UserApiEndpoints.allNews, queryParameters: {
        'page': page,
        'limit': limit,
        'search': search,
        'category_id': category_id,
      });
    } catch (e) {
      rethrow;
    }
  }

  // Get News by Id
  static Future<Response> getNewsById(String newsId) async {
    try {
      return await _apiClient.get('${UserApiEndpoints.newsById}/$newsId');
    } catch (e) {
      rethrow;
    }
  }

  // Get News Category by Id
  static Future<Response> getNewsCategoryById(String categoryId) async {
    try {
      return await _apiClient
          .get('${UserApiEndpoints.newsCategoryById}/$categoryId');
    } catch (e) {
      rethrow;
    }
  }

  // Get All News Categories
  static Future<Response> getNewsCategories() async {
    try {
      return await _apiClient.get(UserApiEndpoints.newsCategory);
    } catch (e) {
      rethrow;
    }
  }

  //Get jOBpOST
  static Future<Response> getJobPost({
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    try {
      return await _apiClient
          .get(UserApiEndpoints.allJobPosts, queryParameters: {
        'page': page,
        'limit': limit,
        'search': search,
      });
    } catch (e) {
      rethrow;
    }
  }

  // Get Job Post by Id
  static Future<Response> getJobPostById(String jobId) async {
    try {
      return await _apiClient.get('${UserApiEndpoints.jobPostById}/$jobId');
    } catch (e) {
      rethrow;
    }
  }

  // Create job seeker profile
  static Future<Response> createJobSeekerProfile(
      JobSeekerProfile profile, String userId) async {
    try {
      return await _apiClient.post(
        '${UserApiEndpoints.jobSeekerOnboarding}/$userId',
        data: profile,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Refresh access token using refresh token
  static Future<Response> refreshToken({required String refreshToken}) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.refreshToken,
        data: {'refresh_token': refreshToken},
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Initialize API client with stored session
  static Future<void> initializeSession() async {
    await _apiClient.initializeWithStoredSession();
  }
}
