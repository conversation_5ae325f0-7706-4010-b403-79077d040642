import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'session_manager.dart';
import 'user_api_endpoints.dart';

/// Custom API interceptor for handling authentication, logging, and error handling
class ApiInterceptor extends Interceptor {
  String? _authToken;
  bool _isRefreshing = false;
  final List<RequestOptions> _pendingRequests = [];

  // Setter for auth token
  void setAuthToken(String? token) {
    _authToken = token;
  }

  // Getter for auth token
  String? get authToken => _authToken;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Add authentication token if available
    if (_authToken != null && _authToken!.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $_authToken';
    }

    // Add common headers
    options.headers['Content-Type'] = 'application/json';
    options.headers['Multipart'] = 'application/json';
    options.headers['Accept'] = 'application/json';

    // Log request in debug mode
    if (kDebugMode) {
      print('🚀 REQUEST[${options.method}] => PATH: ${options.path}');
      print('Headers: ${options.headers}');
      if (options.data != null) {
        print('Data: ${options.data}');
      }
      if (options.queryParameters.isNotEmpty) {
        print('Query Parameters: ${options.queryParameters}');
      }
    }

    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Log response in debug mode
    if (kDebugMode) {
      print(
          '✅ RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}');
      print('Data: ${response.data}');
    }

    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Log error in debug mode
    if (kDebugMode) {
      print(
          '❌ ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}');
      print('Message: ${err.message}');
      if (err.response?.data != null) {
        print('Error Data: ${err.response?.data}');
      }
    }

    // Handle 401 Unauthorized - attempt token refresh
    if (err.response?.statusCode == 401 && !_isRefreshing) {
      await _handleTokenRefresh(err, handler);
      return;
    }

    // Handle specific error cases
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        err = DioException(
          requestOptions: err.requestOptions,
          error: 'Connection timeout. Please check your internet connection.',
          type: err.type,
        );
        break;
      case DioExceptionType.connectionError:
        err = DioException(
          requestOptions: err.requestOptions,
          error: 'No internet connection. Please check your network.',
          type: err.type,
        );
        break;
      case DioExceptionType.badResponse:
        final statusCode = err.response?.statusCode;
        String errorMessage = 'An error occurred';

        switch (statusCode) {
          case 400:
            errorMessage = 'Bad request. Please check your input.';
            break;
          case 401:
            errorMessage = 'Unauthorized. Please login again.';
            // Clear auth token on 401
            _authToken = null;
            break;
          case 403:
            errorMessage = 'Access forbidden.';
            break;
          case 404:
            errorMessage = 'Resource not found.';
            break;
          case 422:
            // Try to extract validation errors
            if (err.response?.data is Map) {
              final data = err.response?.data as Map;
              if (data.containsKey('message')) {
                errorMessage = data['message'];
              } else if (data.containsKey('errors')) {
                errorMessage = 'Validation failed';
              }
            }
            break;
          case 500:
            errorMessage = 'Server error. Please try again later.';
            break;
          default:
            errorMessage = 'An unexpected error occurred.';
        }

        err = DioException(
          requestOptions: err.requestOptions,
          error: errorMessage,
          response: err.response,
          type: err.type,
        );
        break;
      default:
        err = DioException(
          requestOptions: err.requestOptions,
          error: 'An unexpected error occurred.',
          type: err.type,
        );
    }

    super.onError(err, handler);
  }

  /// Handle token refresh when receiving 401 error
  Future<void> _handleTokenRefresh(
      DioException err, ErrorInterceptorHandler handler) async {
    try {
      _isRefreshing = true;

      if (kDebugMode) {
        print('🔄 Attempting token refresh...');
      }

      final sessionManager = SessionManager.instance;
      final refreshToken = await sessionManager.getRefreshToken();

      if (refreshToken == null) {
        if (kDebugMode) {
          print('❌ No refresh token available');
        }
        await sessionManager.clearSession();
        _authToken = null;
        handler.next(err);
        return;
      }

      // Create a new Dio instance to avoid interceptor loops
      final refreshDio = Dio(BaseOptions(
        baseUrl: UserApiEndpoints.fullBaseUrl,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ));

      // Attempt to refresh the token
      final response = await refreshDio.post(
        UserApiEndpoints.refreshToken,
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200 && response.data['access_token'] != null) {
        final data = response.data;
        final newAccessToken = data['access_token'];
        final newRefreshToken = data['refresh_token'] ?? refreshToken;
        final expiresIn = data['expires_in']; // Unix timestamp
        final userData = data['user'];
        final userId = data['user_id'];
        final userType = data['user_type'];

        // Calculate token expiry from expires_in timestamp
        DateTime? tokenExpiry;
        if (expiresIn != null) {
          tokenExpiry = DateTime.fromMillisecondsSinceEpoch(expiresIn * 1000);
        } else {
          // Fallback to 24 hours if expires_in not provided
          tokenExpiry = DateTime.now().add(const Duration(hours: 24));
        }

        // Save complete session with updated data
        await sessionManager.saveSession(
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          userId: userId ?? await sessionManager.getUserId() ?? '',
          userType: userType ?? await sessionManager.getUserType() ?? '',
          user: userData ?? await sessionManager.getUser() ?? {},
          tokenExpiry: tokenExpiry,
        );

        // Update interceptor token
        _authToken = newAccessToken;

        if (kDebugMode) {
          print('✅ Token refreshed successfully');
        }

        // Retry the original request with new token
        final originalRequest = err.requestOptions;
        originalRequest.headers['Authorization'] = 'Bearer $newAccessToken';

        try {
          final retryResponse = await Dio().fetch(originalRequest);
          handler.resolve(retryResponse);
        } catch (retryError) {
          handler.next(err);
        }
      } else {
        if (kDebugMode) {
          print('❌ Token refresh failed');
        }
        await sessionManager.clearSession();
        _authToken = null;
        handler.next(err);
      }
    } catch (refreshError) {
      if (kDebugMode) {
        print('❌ Token refresh error: $refreshError');
      }
      await SessionManager.instance.clearSession();
      _authToken = null;
      handler.next(err);
    } finally {
      _isRefreshing = false;
    }
  }
}
