import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tareek/presentation/user_homepage_screen/discover_screen.dart';
import 'package:tareek/presentation/user_homepage_screen/event_details_screen.dart';

import '../../services/api_service.dart';
import 'news_details_screen.dart';

class HomeScreen extends StatefulWidget {
  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int selectedButton = 0;

  late Map<dynamic, dynamic> _user = {};
  String? _userId;
  List<dynamic> _events = [];
  List<dynamic> _news = [];
  List<dynamic> _popularNews = [];
  List<dynamic> _popularEvents = [];
  bool _isLoadingNews = true;
  bool _isLoadingEvents = true;
  bool _isLoadingPopularNews = true;
  bool _isLoadingPopularEvents = true;

  // Function to handle button selection
  void onButtonPressed(int index) {
    setState(() {
      selectedButton = index;
    });
  }

  @override
  void initState() {
    super.initState();
    _loadUserData();
    getCurrentGreeting();
    getPopularEvents();
    getNews();
    getEvents();
  }

  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();

    final userString = prefs.getString('user');
    final userId = prefs.getString('user_id');

    if (userString != null) {
      final user = jsonDecode(userString);
      setState(() {
        _user = user;
        _userId = userId;
      });
    } else {
      print('No user found. Redirecting...');
    }
  }

  Future<void> getPopularEvents() async {
    try {
      final response = await ApiService.getPopularEvents();
      if (response.statusCode == 200) {
        final responseData = response.data;
        setState(() {
          _popularEvents = responseData['data'] ?? [];
          _isLoadingPopularEvents = false;
        });
        print('Events loaded: ${_popularEvents.length}');
      } else {
        print('Error getting events: ${response.statusCode}');
        setState(() {
          _isLoadingPopularEvents = false;
        });
      }
    } catch (e) {
      print('Error fetching events: $e');
      setState(() {
        _isLoadingPopularEvents = false;
      });
    }
  }

  Future<void> getNews() async {
    try {
      final response = await ApiService.getNews();
      if (response.statusCode == 200) {
        final responseData = response.data;
        setState(() {
          _news = responseData['data'] ?? [];
          _isLoadingNews = false;
        });
        print('News loaded: ${_news.length}');
      } else {
        print('Error getting news: ${response.statusCode}');
        setState(() {
          _isLoadingNews = false;
        });
      }
    } catch (e) {
      print('Error fetching news: $e');
      setState(() {
        _isLoadingNews = false;
      });
    }
  }

  Future<void> getEvents() async {
    try {
      final response = await ApiService.getEvents();
      if (response.statusCode == 200) {
        final responseData = response.data;
        setState(() {
          _events = responseData['data'] ?? [];
          _isLoadingEvents = false;
        });
        print('Events loaded: ${_events.length}');
      } else {
        print('Error getting events: ${response.statusCode}');
        setState(() {
          _isLoadingEvents = false;
        });
      }
    } catch (e) {
      print('Error fetching events: $e');
      setState(() {
        _isLoadingEvents = false;
      });
    }
  }

  String getCurrentGreeting() {
    final hour = DateTime.now().hour;

    if (hour >= 5 && hour < 12) {
      return 'Good morning';
    } else if (hour >= 12 && hour < 17) {
      return 'Good afternoon';
    } else if (hour >= 17 && hour < 21) {
      return 'Good evening';
    } else {
      return 'Good night';
    }
  }

  String formatEventDateTime(String? date, String? time) {
    if (date == null || time == null) return 'Date/time unavailable';

    try {
      // Combine date and time into a single DateTime
      final dateTime = DateTime.parse('$date $time');

      // Format the datetime
      final formatted =
          DateFormat('EEEE MMMM d, y \'at\' HH:mm').format(dateTime);
      return formatted;
    } catch (e) {
      return 'Invalid date/time';
    }
  }

  String formatNewsDate(String? dateString) {
    if (dateString == null) return 'Date unavailable';

    try {
      final date = DateTime.parse(dateString);
      return DateFormat('MMMM d, y').format(date);
    } catch (e) {
      return 'Invalid date';
    }
  }

  void _navigateToEventDetails(Map<String, dynamic> event) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EventDetailsScreen(event: event, userId: _userId!),
      ),
    );
  }

  void _navigateToNewsDetails(Map<String, dynamic> news) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NewsDetailsScreen(news: news, userId: _userId!),
      ),
    );
  }

  Widget _buildNewsCard(Map<String, dynamic> news) {
    return GestureDetector(
      onTap: () => _navigateToNewsDetails(news),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Card(
          elevation: 4,
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image section
              Container(
                height: 180,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  image:
                      news['image_url'] != null && news['image_url'].isNotEmpty
                          ? DecorationImage(
                              image: NetworkImage(news['image_url']),
                              fit: BoxFit.cover,
                              onError: (error, stackTrace) {},
                            )
                          : null,
                  gradient:
                      news['image_url'] == null || news['image_url'].isEmpty
                          ? LinearGradient(
                              colors: [
                                Color(0xFF6366F1),
                                Color(0xFF8B5CF6),
                                Color(0xFFEC4899),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                          : null,
                ),
                child: Stack(
                  children: [
                    // Category badge
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Color(0xFFFF6B35),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          (news['category'] ?? 'News').toUpperCase(),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            fontFamily: "Satoshi",
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Content section
              Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Author and date
                    Row(
                      children: [
                        Icon(
                          Icons.person,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: 4),
                        Text(
                          news['author'] ?? 'Unknown Author',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                            fontFamily: "Satoshi",
                          ),
                        ),
                        Spacer(),
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: 4),
                        Text(
                          formatNewsDate(news['created_at']),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                            fontFamily: "Satoshi",
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    // Title
                    Text(
                      news['title'] ?? 'News Title',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        fontFamily: "Satoshi",
                        height: 1.3,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 8),
                    // Story preview
                    Text(
                      news['story'] ?? 'No story available',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: "Satoshi",
                        color: Colors.grey[700],
                        height: 1.4,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEventCard(Map<String, dynamic> event) {
    final rawDate = event['event_date'];
    DateTime? parsedDate;

    try {
      parsedDate = DateTime.parse(rawDate);
    } catch (e) {
      parsedDate = null;
    }

    final day = parsedDate != null ? DateFormat('d').format(parsedDate) : '??';
    final month = parsedDate != null
        ? DateFormat('MMM').format(parsedDate).toUpperCase()
        : '???';

    return GestureDetector(
      onTap: () => _navigateToEventDetails(event),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Card(
          elevation: 4,
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top section - Image with overlays
              Container(
                height: 140,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  image: event['image_url'] != null &&
                          event['image_url'].isNotEmpty
                      ? DecorationImage(
                          image: NetworkImage(event['image_url']),
                          fit: BoxFit.cover,
                          onError: (error, stackTrace) {},
                        )
                      : null,
                  gradient:
                      event['image_url'] == null || event['image_url'].isEmpty
                          ? LinearGradient(
                              colors: [
                                Color(0xFF6366F1),
                                Color(0xFF8B5CF6),
                                Color(0xFFEC4899),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                          : null,
                ),
                child: Stack(
                  children: [
                    // Date overlay in top left
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            Text(
                              day,
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(0xFF6366F1),
                                fontFamily: "Satoshi",
                              ),
                            ),
                            Text(
                              month,
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          event['location'] ?? 'N/A',
                          style: TextStyle(
                            color: Color(0xFF6366F1),
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            fontFamily: "Satoshi",
                          ),
                        ),
                      ),
                    ),
                    // Category badge
                    Positioned(
                      bottom: 12,
                      left: 12,
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Color(0xFFFF6B35),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          (event['category_name'] ?? 'N/A').toUpperCase(),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            fontFamily: "Satoshi",
                          ),
                        ),
                      ),
                    ),
                    // Charge info
                    Positioned(
                      bottom: 12,
                      right: 12,
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          event['charge'] ?? 'Free',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            fontFamily: "Satoshi",
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Bottom section - Event details
              Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Time info
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: 4),
                        Text(
                          formatEventDateTime(
                              event['event_date'], event['event_time']),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                            fontFamily: "Satoshi",
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    // Event title
                    Text(
                      event['short_desc'] ??
                          event['description'] ??
                          'Event description',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        fontFamily: "Satoshi",
                        height: 1.3,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryContent() {
    switch (selectedButton) {
      case 0: // News
        return _isLoadingNews
            ? Container(
                height: 200,
                child: Center(
                  child: CircularProgressIndicator(
                    color: Color(0xFF1F41BB),
                  ),
                ),
              )
            : _news.isEmpty
                ? Container(
                    height: 200,
                    child: Center(
                      child: Text(
                        'No news available',
                        style: TextStyle(
                          fontFamily: "Satoshi",
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  )
                : Column(
                    children: _news
                        .take(5)
                        .map((news) => _buildNewsCard(news))
                        .toList(),
                  );
      case 1: // Events
        return _isLoadingEvents
            ? Container(
                height: 200,
                child: Center(
                  child: CircularProgressIndicator(
                    color: Color(0xFF1F41BB),
                  ),
                ),
              )
            : _events.isEmpty
                ? Container(
                    height: 200,
                    child: Center(
                      child: Text(
                        'No events available',
                        style: TextStyle(
                          fontFamily: "Satoshi",
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  )
                : Column(
                    children: _events
                        .take(5)
                        .map((event) => _buildEventCard(event))
                        .toList(),
                  );
      case 2: // Markets Place
        return Container(
          height: 200,
          child: Center(
            child: Text(
              'Markets Place - Coming Soon',
              style: TextStyle(
                fontFamily: "Satoshi",
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ),
        );
      default:
        return Container();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Color(0xFF1F41BB),
        elevation: 0,
        automaticallyImplyLeading: false,
        title: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    getCurrentGreeting(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      fontFamily: "Satoshi",
                    ),
                  ),
                  SizedBox(height: 5),
                  Text(
                    _user['name'] ?? '',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      fontFamily: "Satoshi",
                    ),
                  ),
                ],
              ),
              CircleAvatar(
                radius: 25,
                child: ClipOval(
                  child: Image.network(
                    _user['image'] ?? '',
                    fit: BoxFit.cover,
                    width: 50,
                    height: 50,
                    errorBuilder: (context, error, stackTrace) {
                      return Image.asset(
                        'assets/account.jpg',
                        fit: BoxFit.cover,
                        width: 50,
                        height: 50,
                      );
                    },
                  ),
                ),
              )
            ],
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              color: Color(0xFF1F41BB),
              padding: EdgeInsets.all(20),
              child: Column(
                children: [
                  SizedBox(height: 50),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                        bottomLeft: Radius.circular(30),
                        bottomRight: Radius.circular(30),
                      ),
                    ),
                    child: TextField(
                      decoration: InputDecoration(
                        prefixIcon: Icon(Icons.search),
                        suffixIcon: Icon(Icons.filter_list),
                        hintText: 'Search...',
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(vertical: 15),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Popular Now Section
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Text(
                    'Popular Now',
                    style: TextStyle(
                      fontSize: 18,
                      fontFamily: "Satoshi",
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(width: 10),
                  SvgPicture.asset('assets/fire.svg', width: 20, height: 20),
                ],
              ),
            ),

            // Events Section - Display first 5 events
            _isLoadingPopularEvents
                ? Container(
                    height: 200,
                    child: Center(
                      child: CircularProgressIndicator(
                        color: Color(0xFF1F41BB),
                      ),
                    ),
                  )
                : _popularEvents.isEmpty
                    ? Container(
                        height: 200,
                        child: Center(
                          child: Text(
                            'No Popular events available',
                            style: TextStyle(
                              fontFamily: "Satoshi",
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      )
                    : Column(
                        children: _popularEvents.take(5).map((event) {
                          final rawDate = event['event_date']; // "2025-06-30"
                          DateTime? parsedDate;

                          try {
                            parsedDate = DateTime.parse(rawDate);
                          } catch (e) {
                            parsedDate = null;
                          }

                          final day = parsedDate != null
                              ? DateFormat('d').format(parsedDate)
                              : '??';
                          final month = parsedDate != null
                              ? DateFormat('MMM')
                                  .format(parsedDate)
                                  .toUpperCase()
                              : '???';
                          return GestureDetector(
                            onTap: () => _navigateToEventDetails(event),
                            child: Container(
                              margin: EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              child: Card(
                                elevation: 4,
                                color: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Top section - Image with overlays
                                    Container(
                                      height: 140,
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(20),
                                          topRight: Radius.circular(20),
                                        ),
                                        image: event['image_url'] != null &&
                                                event['image_url'].isNotEmpty
                                            ? DecorationImage(
                                                image: NetworkImage(
                                                    event['image_url']),
                                                fit: BoxFit.cover,
                                                onError: (error, stackTrace) {},
                                              )
                                            : null,
                                        gradient: event['image_url'] == null ||
                                                event['image_url'].isEmpty
                                            ? LinearGradient(
                                                colors: [
                                                  Color(0xFF6366F1),
                                                  Color(0xFF8B5CF6),
                                                  Color(0xFFEC4899),
                                                ],
                                                begin: Alignment.topLeft,
                                                end: Alignment.bottomRight,
                                              )
                                            : null,
                                      ),
                                      child: Stack(
                                        children: [
                                          // Date overlay in top left
                                          Positioned(
                                            top: 12,
                                            left: 12,
                                            child: Container(
                                              padding: EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                              child: Column(
                                                children: [
                                                  Text(
                                                    day,
                                                    // You can extract day from event['event_date']
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                      color: Color(0xFF6366F1),
                                                      fontFamily: "Satoshi",
                                                    ),
                                                  ),
                                                  Text(
                                                    month,
                                                    style: TextStyle(
                                                      fontSize: 10,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: Colors.black54,
                                                      fontFamily: "Satoshi",
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            top: 12,
                                            right: 12,
                                            child: Container(
                                              padding: EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                              child: Column(
                                                children: [
                                                  Text(
                                                    event['location'] ?? 'N/A',
                                                    style: TextStyle(
                                                      color: Color(0xFF6366F1),
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      fontFamily: "Satoshi",
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),

                                          // Category badge in bottom left
                                          Positioned(
                                            bottom: 12,
                                            left: 12,
                                            child: Container(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 12, vertical: 6),
                                              decoration: BoxDecoration(
                                                color: Color(0xFFFF6B35),
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                              ),
                                              child: Text(
                                                (event['category_name'] ??
                                                        'N/A')
                                                    .toUpperCase(),
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.bold,
                                                  fontFamily: "Satoshi",
                                                ),
                                              ),
                                            ),
                                          ),

                                          // Cover text in bottom right
                                          Positioned(
                                            bottom: 12,
                                            right: 12,
                                            child: Container(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 8, vertical: 4),
                                              decoration: BoxDecoration(
                                                color: Colors.black
                                                    .withOpacity(0.6),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              child: Text(
                                                event['charge'] ?? 'N/A',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 10,
                                                  fontWeight: FontWeight.w600,
                                                  fontFamily: "Satoshi",
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    // Bottom section - Event details
                                    Padding(
                                      padding: EdgeInsets.all(16),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // Time info
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.access_time,
                                                size: 14,
                                                color: Colors.grey[600],
                                              ),
                                              SizedBox(width: 4),
                                              Text(
                                                formatEventDateTime(
                                                    event['event_date'],
                                                    event['event_time']),
                                                style: TextStyle(
                                                  color: Colors.grey[600],
                                                  fontSize: 12,
                                                  fontFamily: "Satoshi",
                                                ),
                                              ),
                                              Spacer(),
                                            ],
                                          ),

                                          SizedBox(height: 8),

                                          // Event title
                                          Text(
                                            event['short_desc'] ??
                                                event['description'] ??
                                                'Event description',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                              fontFamily: "Satoshi",
                                              height: 1.3,
                                              color: Colors.black87,
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
            SizedBox(height: 20),

            // Category Section
            Padding(
              padding: EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Category',
                    style: TextStyle(
                      fontSize: 18,
                      fontFamily: "Satoshi",
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => DiscoverScreen(),
                        ),
                      );
                    },
                    child: Text(
                      'See All',
                      style: TextStyle(
                        fontFamily: "Satoshi",
                        color: Color(0xFF1F41BB),
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Row of Buttons
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildCategoryButton(0, 'News', 'assets/fire.svg'),
                    SizedBox(width: 10),
                    _buildCategoryButton(1, 'Events', 'assets/airplay.svg'),
                    SizedBox(width: 10),
                    _buildCategoryButton(2, 'Markets Place', 'assets/bar.svg'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            _buildCategoryContent(),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryButton(int index, String title, String iconPath) {
    return ElevatedButton(
      onPressed: () => onButtonPressed(index),
      style: ElevatedButton.styleFrom(
        foregroundColor:
            selectedButton == index ? Colors.white : Color(0xFF1F41BB),
        backgroundColor:
            selectedButton == index ? Color(0xFF1F41BB) : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        side: BorderSide(color: Color(0xFF1F41BB)),
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(iconPath, width: 20, height: 20),
          SizedBox(width: 8),
          Text(title),
        ],
      ),
    );
  }
}
